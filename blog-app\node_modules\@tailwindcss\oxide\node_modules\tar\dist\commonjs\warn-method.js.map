{"version": 3, "file": "warn-method.js", "sourceRoot": "", "sources": ["../../src/warn-method.ts"], "names": [], "mappings": ";;;AAiCO,MAAM,UAAU,GAAG,CACxB,IAAY,EACZ,IAAY,EACZ,OAAuB,EACvB,OAAiB,EAAE,EACnB,EAAE;IACF,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;IACrB,CAAC;IACD,IAAI,CAAC,IAAI;QACP,CAAC,OAAO,YAAY,KAAK;YACtB,OAAiC,CAAC,IAAI,CAAC;YAC1C,IAAI,CAAA;IACN,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;QAC/C,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;YAC7B,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YACnC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC3B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;IACxC,CAAC;SAAM,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;IAClD,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CACP,OAAO,EACP,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CACtD,CAAA;IACH,CAAC;AACH,CAAC,CAAA;AA/BY,QAAA,UAAU,cA+BtB", "sourcesContent": ["import { type Minipass } from 'minipass'\n\n/** has a warn method */\nexport type Warner = {\n  warn(code: string, message: string | Error, data: any): void\n  file?: string\n  cwd?: string\n  strict?: boolean\n\n  emit(\n    event: 'warn',\n    code: string,\n    message: string,\n    data?: WarnData,\n  ): void\n  emit(event: 'error', error: TarError): void\n}\n\nexport type WarnEvent<T = Buffer> = Minipass.Events<T> & {\n  warn: [code: string, message: string, data: WarnData]\n}\n\nexport type WarnData = {\n  file?: string\n  cwd?: string\n  code?: string\n  tarCode?: string\n  recoverable?: boolean\n  [k: string]: any\n}\n\nexport type TarError = Error & WarnData\n\nexport const warnMethod = (\n  self: Warner,\n  code: string,\n  message: string | Error,\n  data: WarnData = {},\n) => {\n  if (self.file) {\n    data.file = self.file\n  }\n  if (self.cwd) {\n    data.cwd = self.cwd\n  }\n  data.code =\n    (message instanceof Error &&\n      (message as NodeJS.ErrnoException).code) ||\n    code\n  data.tarCode = code\n  if (!self.strict && data.recoverable !== false) {\n    if (message instanceof Error) {\n      data = Object.assign(message, data)\n      message = message.message\n    }\n    self.emit('warn', code, message, data)\n  } else if (message instanceof Error) {\n    self.emit('error', Object.assign(message, data))\n  } else {\n    self.emit(\n      'error',\n      Object.assign(new Error(`${code}: ${message}`), data),\n    )\n  }\n}\n"]}