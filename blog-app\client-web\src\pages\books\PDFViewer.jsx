import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

const PDFViewer = () => {
  const { id } = useParams();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // TODO: Implement PDF viewer with react-pdf
    // For now, just show a placeholder
    setLoading(false);
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">PDF Viewer</h1>
          <p className="text-gray-600 mb-8">
            PDF viewer will be implemented in the next phase with react-pdf library.
          </p>
          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-12">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500">PDF content will be displayed here</p>
            <p className="text-sm text-gray-400 mt-2">Book ID: {id}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;
