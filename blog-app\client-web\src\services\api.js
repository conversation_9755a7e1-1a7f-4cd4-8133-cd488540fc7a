import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/users/logout'),
  getCurrentUser: () => api.get('/auth/me'),
  refreshToken: (refreshToken) => api.post('/auth/refresh', { refreshToken }),
};

// Users API
export const usersAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData) => api.put('/users/profile', userData),
  getHistory: (page = 1, limit = 10) => api.get(`/users/history?page=${page}&limit=${limit}`),
  getFavorites: () => api.get('/users/favorites'),
  addToFavorites: (bookId) => api.post(`/users/favorites/${bookId}`),
  removeFromFavorites: (bookId) => api.delete(`/users/favorites/${bookId}`),
  getStats: () => api.get('/users/stats'),
};

// Books API
export const booksAPI = {
  getBooks: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/books?${queryString}`);
  },
  getBook: (id) => api.get(`/books/${id}`),
  downloadBook: (id) => api.get(`/books/${id}/download`, { responseType: 'blob' }),
  searchBooks: (searchData) => api.post('/books/search', searchData),
  getCategories: () => api.get('/books/meta/categories'),
  
  // Annotations
  getAnnotations: (bookId, page = null) => {
    const url = page ? `/books/${bookId}/annotations?page=${page}` : `/books/${bookId}/annotations`;
    return api.get(url);
  },
  saveAnnotation: (bookId, annotationData) => api.post(`/books/${bookId}/annotations`, annotationData),
  updateAnnotation: (bookId, annotationId, annotationData) => 
    api.put(`/books/${bookId}/annotations/${annotationId}`, annotationData),
  deleteAnnotation: (bookId, annotationId) => api.delete(`/books/${bookId}/annotations/${annotationId}`),
};

// Admin API
export const adminAPI = {
  // Analytics
  getAnalytics: () => api.get('/admin/analytics'),
  
  // Books management
  getBooks: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/admin/books?${queryString}`);
  },
  uploadBook: (formData) => api.post('/admin/books', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateBook: (id, bookData) => api.put(`/admin/books/${id}`, bookData),
  deleteBook: (id) => api.delete(`/admin/books/${id}`),
  
  // Users management
  getUsers: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/admin/users?${queryString}`);
  },
  updateUser: (id, userData) => api.put(`/admin/users/${id}`, userData),
  
  // Payments
  getPayments: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/admin/payments?${queryString}`);
  },
};

// Payments API
export const paymentsAPI = {
  getHistory: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/payments/history?${queryString}`);
  },
  processStripePayment: (paymentData) => api.post('/payments/stripe', paymentData),
  processMpesaPayment: (paymentData) => api.post('/payments/mpesa', paymentData),
  verifyPayment: (paymentId) => api.post('/payments/verify', { paymentId }),
  getSubscription: () => api.get('/payments/subscription'),
};

export default api;
